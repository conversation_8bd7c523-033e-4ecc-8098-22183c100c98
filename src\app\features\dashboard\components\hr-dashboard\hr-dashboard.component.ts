import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { RequestService } from '../../../../core/services/request.service';
import { UserService } from '../../../../core/services/user.service';
import { LeaveService } from '../../../../core/services/leave.service';
import { RequestDto, RequestStatus, RequestType, PaginationParams } from '../../../../core/models';

interface HRMetrics {
  totalEmployees: number;
  activeRequests: number;
  processedThisMonth: number;
  averageProcessingTime: number;
  leaveRequests: number;
  expenseReports: number;
  trainingRequests: number;
}

@Component({
  selector: 'app-hr-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatChipsModule,
    MatTabsModule,
    MatProgressBarModule
  ],
  template: `
    <div class="hr-dashboard">
      <div class="dashboard-header">
        <h1>HR Dashboard</h1>
        <p>Manage employee requests and HR processes</p>
      </div>

      <!-- Key Metrics -->
      <div class="metrics-grid">
        <mat-card class="metric-card employees">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>people</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.totalEmployees}}</h3>
                <p>Total Employees</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card active">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>assignment</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.activeRequests}}</h3>
                <p>Active Requests</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card processed">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>done_all</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.processedThisMonth}}</h3>
                <p>Processed This Month</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="metric-card time">
          <mat-card-content>
            <div class="metric">
              <div class="metric-icon">
                <mat-icon>schedule</mat-icon>
              </div>
              <div class="metric-info">
                <h3>{{hrMetrics.averageProcessingTime}}h</h3>
                <p>Avg. Processing Time</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Request Type Breakdown -->
      <div class="request-types-grid">
        <mat-card class="type-card leave">
          <mat-card-content>
            <div class="type-metric">
              <mat-icon>event_available</mat-icon>
              <div class="type-info">
                <h4>{{hrMetrics.leaveRequests}}</h4>
                <p>Leave Requests</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="type-card expense">
          <mat-card-content>
            <div class="type-metric">
              <mat-icon>receipt</mat-icon>
              <div class="type-info">
                <h4>{{hrMetrics.expenseReports}}</h4>
                <p>Expense Reports</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="type-card training">
          <mat-card-content>
            <div class="type-metric">
              <mat-icon>school</mat-icon>
              <div class="type-info">
                <h4>{{hrMetrics.trainingRequests}}</h4>
                <p>Training Requests</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Main Content Tabs -->
      <mat-card class="content-card">
        <mat-tab-group>
          <!-- Pending HR Review -->
          <mat-tab label="Pending Review ({{pendingHRRequests.length}})">
            <div class="tab-content">
              <div class="tab-header">
                <h3>Requests Awaiting HR Review</h3>
                <button mat-raised-button color="primary" routerLink="/requests">
                  <mat-icon>list</mat-icon>
                  View All Requests
                </button>
              </div>

              <div *ngIf="pendingHRRequests.length > 0" class="requests-table">
                <table mat-table [dataSource]="pendingHRRequests">
                  <!-- Employee Column -->
                  <ng-container matColumnDef="employee">
                    <th mat-header-cell *matHeaderCellDef>Employee</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="employee-info">
                        <strong>{{request.initiatorName}}</strong>
                        <small>{{request.createdAt | date:'short'}}</small>
                      </div>
                    </td>
                  </ng-container>

                  <!-- Request Type Column -->
                  <ng-container matColumnDef="type">
                    <th mat-header-cell *matHeaderCellDef>Type</th>
                    <td mat-cell *matCellDef="let request">
                      <mat-chip [class]="getRequestTypeClass(request.type)">
                        {{getRequestTypeLabel(request.type)}}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Title Column -->
                  <ng-container matColumnDef="title">
                    <th mat-header-cell *matHeaderCellDef>Request</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="request-title">
                        {{request.title || 'No Title'}}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Current Step Column -->
                  <ng-container matColumnDef="currentStep">
                    <th mat-header-cell *matHeaderCellDef>Current Step</th>
                    <td mat-cell *matCellDef="let request">
                      <span class="current-step">{{getCurrentStepName(request)}}</span>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let request">
                      <div class="action-buttons">
                        <button mat-icon-button [routerLink]="['/requests/details', request.id]">
                          <mat-icon>visibility</mat-icon>
                        </button>
                        <button mat-raised-button color="primary" (click)="processRequest(request)">
                          <mat-icon>check</mat-icon>
                          Process
                        </button>
                      </div>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="hrColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: hrColumns;"></tr>
                </table>
              </div>

              <div *ngIf="pendingHRRequests.length === 0" class="no-data">
                <mat-icon>assignment_turned_in</mat-icon>
                <h3>No Pending Reviews</h3>
                <p>All requests have been processed or there are no requests requiring HR review.</p>
              </div>
            </div>
          </mat-tab>

          <!-- Employee Management -->
          <mat-tab label="Employee Management">
            <div class="tab-content">
              <div class="employee-stats">
                <div class="stat-section">
                  <h4>Department Breakdown</h4>
                  <div class="department-list">
                    <div *ngFor="let dept of departments" class="department-item">
                      <span class="dept-name">{{dept.name}}</span>
                      <span class="dept-count">{{dept.count}} employees</span>
                      <mat-progress-bar mode="determinate" [value]="dept.percentage"></mat-progress-bar>
                    </div>
                  </div>
                </div>

                <div class="stat-section">
                  <h4>Recent Hires</h4>
                  <div class="recent-hires">
                    <div *ngFor="let hire of recentHires" class="hire-item">
                      <div class="hire-avatar">{{getInitials(hire.name)}}</div>
                      <div class="hire-info">
                        <strong>{{hire.name}}</strong>
                        <small>{{hire.department}} - {{hire.startDate | date:'short'}}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Analytics -->
          <mat-tab label="Analytics">
            <div class="tab-content">
              <div class="analytics-grid">
                <div class="analytics-card">
                  <h4>Request Volume Trends</h4>
                  <div class="chart-placeholder">
                    <mat-icon>trending_up</mat-icon>
                    <p>Request volume chart would be displayed here</p>
                  </div>
                </div>

                <div class="analytics-card">
                  <h4>Processing Efficiency</h4>
                  <div class="efficiency-metrics">
                    <div class="efficiency-item">
                      <span class="label">Average Processing Time:</span>
                      <span class="value">{{hrMetrics.averageProcessingTime}} hours</span>
                    </div>
                    <div class="efficiency-item">
                      <span class="label">Requests Processed Today:</span>
                      <span class="value">{{dailyProcessed}}</span>
                    </div>
                    <div class="efficiency-item">
                      <span class="label">Efficiency Score:</span>
                      <span class="value">{{efficiencyScore}}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card>
    </div>
  `,
  styles: [`
    .hr-dashboard {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .dashboard-header {
      margin-bottom: 2rem;
    }

    .dashboard-header h1 {
      margin: 0;
      color: #333;
    }

    .dashboard-header p {
      margin: 0.5rem 0 0 0;
      color: #666;
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .metric-card {
      transition: transform 0.2s ease;
    }

    .metric-card:hover {
      transform: translateY(-2px);
    }

    .metric-card.employees {
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      color: white;
    }

    .metric-card.active {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      color: white;
    }

    .metric-card.processed {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
      color: white;
    }

    .metric-card.time {
      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
      color: white;
    }

    .metric {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .metric-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(255, 255, 255, 0.2);
    }

    .metric-icon mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .metric-info h3 {
      margin: 0;
      font-size: 2rem;
      font-weight: bold;
    }

    .metric-info p {
      margin: 0.25rem 0 0 0;
      font-size: 1rem;
    }

    .request-types-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .type-card {
      background-color: #f8f9fa;
      border-left: 4px solid;
    }

    .type-card.leave {
      border-left-color: #2196f3;
    }

    .type-card.expense {
      border-left-color: #ff9800;
    }

    .type-card.training {
      border-left-color: #4caf50;
    }

    .type-metric {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .type-metric mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: #666;
    }

    .type-info h4 {
      margin: 0;
      font-size: 1.5rem;
      color: #333;
    }

    .type-info p {
      margin: 0.25rem 0 0 0;
      color: #666;
    }

    .content-card {
      margin-top: 1rem;
    }

    .tab-content {
      padding: 1rem;
    }

    .tab-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .requests-table {
      overflow-x: auto;
    }

    .requests-table table {
      width: 100%;
    }

    .employee-info {
      display: flex;
      flex-direction: column;
    }

    .employee-info small {
      color: #666;
      font-size: 0.8rem;
    }

    .request-title {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .current-step {
      background-color: #e3f2fd;
      color: #1976d2;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
    }

    .action-buttons {
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }

    .type-leave { background-color: #e3f2fd; color: #1976d2; }
    .type-expense { background-color: #f3e5f5; color: #7b1fa2; }
    .type-training { background-color: #e8f5e8; color: #2e7d32; }
    .type-it { background-color: #fff3e0; color: #ef6c00; }
    .type-profile { background-color: #fce4ec; color: #c2185b; }

    .no-data {
      text-align: center;
      padding: 3rem;
      color: #666;
    }

    .no-data mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      color: #4caf50;
    }

    .employee-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .stat-section {
      padding: 1rem;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }

    .stat-section h4 {
      margin: 0 0 1rem 0;
      color: #333;
    }

    .department-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .department-item {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .dept-name {
      font-weight: 500;
    }

    .dept-count {
      font-size: 0.8rem;
      color: #666;
    }

    .recent-hires {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .hire-item {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .hire-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #2196f3;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.9rem;
    }

    .hire-info {
      display: flex;
      flex-direction: column;
    }

    .hire-info small {
      color: #666;
      font-size: 0.8rem;
    }

    .analytics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .analytics-card {
      padding: 1rem;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }

    .analytics-card h4 {
      margin: 0 0 1rem 0;
      color: #333;
    }

    .chart-placeholder {
      text-align: center;
      padding: 2rem;
      color: #666;
    }

    .chart-placeholder mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      margin-bottom: 1rem;
    }

    .efficiency-metrics {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .efficiency-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem;
      background-color: white;
      border-radius: 4px;
    }

    .efficiency-item .label {
      font-weight: 500;
    }

    .efficiency-item .value {
      color: #2196f3;
      font-weight: bold;
    }

    @media (max-width: 768px) {
      .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .request-types-grid {
        grid-template-columns: 1fr;
      }

      .tab-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
      }

      .action-buttons {
        flex-direction: column;
      }

      .employee-stats,
      .analytics-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .metrics-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class HRDashboardComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  hrMetrics: HRMetrics = {
    totalEmployees: 45,
    activeRequests: 12,
    processedThisMonth: 67,
    averageProcessingTime: 6.5,
    leaveRequests: 8,
    expenseReports: 15,
    trainingRequests: 4
  };

  pendingHRRequests: RequestDto[] = [];
  hrColumns: string[] = ['employee', 'type', 'title', 'currentStep', 'actions'];

  departments = [
    { name: 'Engineering', count: 18, percentage: 40 },
    { name: 'Sales', count: 12, percentage: 27 },
    { name: 'Marketing', count: 8, percentage: 18 },
    { name: 'HR', count: 4, percentage: 9 },
    { name: 'Finance', count: 3, percentage: 6 }
  ];

  recentHires: { name: string; department: string; startDate: Date }[] = [];

  dailyProcessed = 5;
  efficiencyScore = 92;

  constructor(
    private readonly requestService: RequestService,
    private readonly userService: UserService,
    private readonly leaveService: LeaveService
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadDashboardData(): void {
    this.loadPendingHRRequests();
    this.loadHRMetrics();
    this.loadUserStats();
    this.loadRecentHires();
  }

  loadPendingHRRequests(): void {
    const params: PaginationParams = {
      pageNumber: 1,
      pageSize: 10,
      status: RequestStatus.Pending
    };

    this.requestService.getPendingApprovals(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          // Filter for requests that need HR processing
          this.pendingHRRequests = response.data.filter(request =>
            request.requestSteps?.some(step =>
              step.responsibleRole === 'HR' && step.status === 1 // Pending status
            )
          );
        },
        error: (error) => {
          console.error('Error loading pending HR requests:', error);
          this.pendingHRRequests = [];
        }
      });
  }

  loadHRMetrics(): void {
    // Load request statistics
    this.requestService.getRequests({ pageNumber: 1, pageSize: 1000 })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          const requests = response.data;
          this.hrMetrics.activeRequests = requests.filter(r => r.status === RequestStatus.Pending).length;
          this.hrMetrics.processedThisMonth = requests.filter(r => {
            const createdDate = new Date(r.createdAt);
            const now = new Date();
            return createdDate.getMonth() === now.getMonth() &&
                   createdDate.getFullYear() === now.getFullYear() &&
                   r.status !== RequestStatus.Pending;
          }).length;

          // Count by type
          this.hrMetrics.leaveRequests = requests.filter(r => r.type === RequestType.Leave).length;
          this.hrMetrics.expenseReports = requests.filter(r => r.type === RequestType.Expense).length;
          this.hrMetrics.trainingRequests = requests.filter(r => r.type === RequestType.Training).length;
        },
        error: (error) => {
          console.error('Error loading HR metrics:', error);
        }
      });
  }

  loadUserStats(): void {
    this.userService.getUserStats()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (stats) => {
          this.hrMetrics.totalEmployees = stats.totalUsers;

          // Update department statistics if available
          if (stats.usersByDepartment) {
            this.departments = Object.entries(stats.usersByDepartment).map(([name, count]) => ({
              name,
              count: Number(count),
              percentage: Math.round((Number(count) / stats.totalUsers) * 100)
            }));
          }
        },
        error: (error) => {
          console.error('Error loading user stats:', error);
          // Keep default values if API fails
        }
      });
  }

  loadRecentHires(): void {
    // Load recent users (last 30 days) to show as recent hires
    const params: PaginationParams = {
      pageNumber: 1,
      pageSize: 10,
      sortBy: 'createdAt',
      sortDirection: 'desc'
    };

    this.userService.getUsers(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (users) => {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

          // Since UserDto doesn't have createdAt, we'll show all users as recent hires
          // In a real scenario, you might want to use a different endpoint that returns creation dates
          this.recentHires = users
            .slice(0, 5) // Show only top 5
            .map(user => ({
              name: `${user.firstName || user.FirstName || ''} ${user.lastName || user.LastName || ''}`.trim() || user.userName || user.UserName || 'Unknown',
              department: 'General', // Default since user service doesn't have department info
              startDate: new Date() // Use current date as fallback since createdAt is not available
            }));
        },
        error: (error) => {
          console.error('Error loading recent hires:', error);
          this.recentHires = [];
        }
      });
  }

  getRequestTypeLabel(type: RequestType): string {
    switch (type) {
      case RequestType.Leave: return 'Leave';
      case RequestType.Expense: return 'Expense';
      case RequestType.Training: return 'Training';
      case RequestType.ITSupport: return 'IT Support';
      case RequestType.ProfileUpdate: return 'Profile';
      default: return 'Unknown';
    }
  }

  getRequestTypeClass(type: RequestType): string {
    switch (type) {
      case RequestType.Leave: return 'type-leave';
      case RequestType.Expense: return 'type-expense';
      case RequestType.Training: return 'type-training';
      case RequestType.ITSupport: return 'type-it';
      case RequestType.ProfileUpdate: return 'type-profile';
      default: return '';
    }
  }

  getCurrentStepName(request: RequestDto): string {
    const currentStep = request.requestSteps?.find(step => step.status === 1); // Pending status
    return currentStep?.workflowStepName || 'Unknown';
  }

  processRequest(request: RequestDto): void {
    console.log('Process request:', request.id);
    // In a real app, navigate to processing page or open dialog
  }

  getInitials(name: string): string {
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
  }
}
