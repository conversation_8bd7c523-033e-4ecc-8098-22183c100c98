import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { UserService } from '../../../../core/services/user.service';
import { UserDto, PaginationParams } from '../../../../core/models';

interface LocalEmployee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  department: string;
  position: string;
  hireDate: Date;
  status: 'Active' | 'Inactive' | 'On Leave';
  manager: string;
  phoneNumber?: string;
  emergencyContact?: string;
}

@Component({
  selector: 'app-employee-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatChipsModule,
    MatPaginatorModule,
    MatTooltipModule,
    MatTabsModule,
    MatMenuModule,
    MatDividerModule
  ],
  template: `
    <div class="employee-management">
      <div class="header">
        <h1>
          <mat-icon>badge</mat-icon>
          Employee Management
        </h1>
        <p>Manage employee records and information</p>
      </div>

      <mat-card class="filters-card">
        <mat-card-content>
          <div class="filters-row">
            <mat-form-field appearance="outline">
              <mat-label>Search Employees</mat-label>
              <input matInput [(ngModel)]="searchTerm" (ngModelChange)="applyFilters()" placeholder="Name, email, or ID">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Department</mat-label>
              <mat-select [(ngModel)]="selectedDepartment" (selectionChange)="applyFilters()">
                <mat-option value="">All Departments</mat-option>
                <mat-option *ngFor="let dept of departments" [value]="dept">{{dept}}</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select [(ngModel)]="selectedStatus" (selectionChange)="applyFilters()">
                <mat-option value="">All Status</mat-option>
                <mat-option value="Active">Active</mat-option>
                <mat-option value="Inactive">Inactive</mat-option>
                <mat-option value="On Leave">On Leave</mat-option>
              </mat-select>
            </mat-form-field>

            <button mat-raised-button color="primary" (click)="addEmployee()">
              <mat-icon>person_add</mat-icon>
              Add Employee
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="employees-table-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>people</mat-icon>
            Employee Directory ({{filteredEmployees.length}})
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="filteredEmployees" class="employees-table">
              <!-- Avatar Column -->
              <ng-container matColumnDef="avatar">
                <th mat-header-cell *matHeaderCellDef></th>
                <td mat-cell *matCellDef="let employee">
                  <div class="employee-avatar">
                    {{getInitials(employee.firstName, employee.lastName)}}
                  </div>
                </td>
              </ng-container>

              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let employee">
                  <div class="employee-info">
                    <strong>{{employee.firstName}} {{employee.lastName}}</strong>
                    <small>{{employee.email}}</small>
                  </div>
                </td>
              </ng-container>

              <!-- Department Column -->
              <ng-container matColumnDef="department">
                <th mat-header-cell *matHeaderCellDef>Department</th>
                <td mat-cell *matCellDef="let employee">{{employee.department}}</td>
              </ng-container>

              <!-- Manager Column -->
              <ng-container matColumnDef="manager">
                <th mat-header-cell *matHeaderCellDef>Manager</th>
                <td mat-cell *matCellDef="let employee">{{employee.manager}}</td>
              </ng-container>

              <!-- Hire Date Column -->
              <ng-container matColumnDef="hireDate">
                <th mat-header-cell *matHeaderCellDef>Hire Date</th>
                <td mat-cell *matCellDef="let employee">{{employee.hireDate | date:'shortDate'}}</td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let employee">
                  <mat-chip [class]="getStatusClass(employee.status)">
                    {{employee.status}}
                  </mat-chip>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let employee">
                  <div class="action-buttons">
                    <button mat-icon-button (click)="viewEmployee(employee)" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button (click)="editEmployee(employee)" matTooltip="Edit Employee">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button (click)="viewRequests(employee)" matTooltip="View Requests">
                      <mat-icon>assignment</mat-icon>
                    </button>
                    <button mat-icon-button [matMenuTriggerFor]="actionMenu" matTooltip="More Actions">
                      <mat-icon>more_vert</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Action Menu -->
    <mat-menu #actionMenu="matMenu">
      <button mat-menu-item (click)="changeStatus()">
        <mat-icon>swap_horiz</mat-icon>
        <span>Change Status</span>
      </button>
      <button mat-menu-item (click)="assignManager()">
        <mat-icon>supervisor_account</mat-icon>
        <span>Assign Manager</span>
      </button>
      <button mat-menu-item (click)="generateReport()">
        <mat-icon>description</mat-icon>
        <span>Generate Report</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item (click)="deactivateEmployee()" class="warn-action">
        <mat-icon>person_off</mat-icon>
        <span>Deactivate</span>
      </button>
    </mat-menu>
  `,
  styles: [`
    .employee-management {
      padding: 1rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      margin-bottom: 2rem;
      text-align: center;
    }

    .filters-card {
      margin-bottom: 1.5rem;
    }

    .filters-row {
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;
    }

    .filters-row mat-form-field {
      min-width: 200px;
    }

    .employees-table-card {
      margin-bottom: 2rem;
    }

    .table-container {
      overflow-x: auto;
    }

    .employees-table {
      width: 100%;
      min-width: 800px;
    }

    .employee-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 0.875rem;
    }

    .employee-info {
      display: flex;
      flex-direction: column;
    }

    .employee-info strong {
      font-weight: 500;
      margin-bottom: 0.25rem;
    }

    .employee-info small {
      color: #666;
      font-size: 0.75rem;
    }

    .action-buttons {
      display: flex;
      gap: 0.25rem;
    }

    .status-active {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-inactive {
      background-color: #ffebee;
      color: #c62828;
    }

    .status-on-leave {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    .warn-action {
      color: #d32f2f;
    }

    @media (max-width: 768px) {
      .filters-row {
        flex-direction: column;
        align-items: stretch;
      }

      .filters-row mat-form-field {
        min-width: auto;
        width: 100%;
      }

      .table-container {
        margin: 0 -1rem;
      }
    }
  `]
})
export class EmployeeManagementComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  employees: LocalEmployee[] = [];
  filteredEmployees: LocalEmployee[] = [];
  displayedColumns: string[] = ['avatar', 'name', 'department', 'manager', 'hireDate', 'status', 'actions'];

  searchTerm = '';
  selectedDepartment = '';
  selectedStatus = '';
  pageSize = 25;
  isLoading = false;

  departments: string[] = [];
  departmentStats: { department: string; count: number; percentage: number }[] = [];

  constructor(private readonly userService: UserService) {}

  ngOnInit(): void {
    this.loadEmployees();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadEmployees(): void {
    this.isLoading = true;

    const params: PaginationParams = {
      pageNumber: 1,
      pageSize: 1000, // Load all employees for HR management
      sortBy: 'firstName',
      sortDirection: 'asc'
    };

    this.userService.getUsers(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (users) => {
          this.employees = users.map(user => this.mapUserToEmployee(user));
          this.updateDepartmentStats();
          this.applyFilters();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading employees:', error);
          this.employees = [];
          this.filteredEmployees = [];
          this.isLoading = false;
        }
      });
  }

  private mapUserToEmployee(user: UserDto): LocalEmployee {
    return {
      id: user.id || user.Id || '',
      firstName: user.firstName || user.FirstName || '',
      lastName: user.lastName || user.LastName || '',
      email: user.email || user.Email || '',
      department: 'General', // Default department since UserDto doesn't have department
      position: 'Employee', // Default position since UserDto doesn't have position
      hireDate: new Date(), // Use current date as fallback since UserDto doesn't have createdAt
      status: 'Active', // Default status
      manager: 'TBD', // Default manager
      phoneNumber: user.phoneNumber || user.PhoneNumber
    };
  }

  private updateDepartmentStats(): void {
    const departmentCounts: { [key: string]: number } = {};

    this.employees.forEach(employee => {
      departmentCounts[employee.department] = (departmentCounts[employee.department] || 0) + 1;
    });

    this.departments = Object.keys(departmentCounts);
    this.departmentStats = Object.entries(departmentCounts).map(([department, count]) => ({
      department,
      count,
      percentage: Math.round((count / this.employees.length) * 100)
    }));
  }

  applyFilters(): void {
    this.filteredEmployees = this.employees.filter(employee => {
      const matchesSearch = !this.searchTerm || 
        employee.firstName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        employee.lastName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        employee.email.toLowerCase().includes(this.searchTerm.toLowerCase());
      
      const matchesDepartment = !this.selectedDepartment || employee.department === this.selectedDepartment;
      const matchesStatus = !this.selectedStatus || employee.status === this.selectedStatus;
      
      return matchesSearch && matchesDepartment && matchesStatus;
    });
  }

  getInitials(firstName: string, lastName: string): string {
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
  }

  onSearchChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.applyFilters();
  }

  getStatusClass(status: string): string {
    return `status-${status.toLowerCase().replace(' ', '-')}`;
  }

  addEmployee(): void {
    console.log('Add employee dialog would open');
  }

  viewEmployee(employee: LocalEmployee): void {
    console.log('View employee:', employee);
  }

  editEmployee(employee: LocalEmployee): void {
    console.log('Edit employee:', employee);
  }

  viewRequests(employee: LocalEmployee): void {
    console.log('View requests for:', employee);
  }

  changeStatus(): void {
    console.log('Change status');
  }

  assignManager(): void {
    console.log('Assign manager');
  }

  generateReport(): void {
    console.log('Generate report');
  }

  deactivateEmployee(): void {
    console.log('Deactivate employee');
  }
}
