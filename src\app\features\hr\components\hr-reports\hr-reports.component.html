<div class="hr-reports-container">
  <!-- Header -->
  <div class="reports-header">
    <div class="header-content">
      <h1><i class="fas fa-chart-bar"></i> HR Reports & Analytics</h1>
      <p>Comprehensive leave management reports and statistics</p>
    </div>
    <div class="header-actions">
      <button class="btn btn-outline-primary" (click)="exportReport('excel')">
        <i class="fas fa-file-excel"></i>
        Export Excel
      </button>
      <button class="btn btn-outline-secondary" (click)="exportReport('pdf')">
        <i class="fas fa-file-pdf"></i>
        Export PDF
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div class="filters-section">
    <div class="filter-card">
      <h3><i class="fas fa-filter"></i> Report Filters</h3>
      <form [formGroup]="filterForm" (ngSubmit)="onFilterChange()">
        <div class="filter-row">
          <div class="form-group">
            <label for="startDate">Start Date</label>
            <input
              type="date"
              id="startDate"
              formControlName="startDate"
              class="form-control"
              (change)="onFilterChange()"
            />
          </div>
          <div class="form-group">
            <label for="endDate">End Date</label>
            <input
              type="date"
              id="endDate"
              formControlName="endDate"
              class="form-control"
              (change)="onFilterChange()"
            />
          </div>
          <div class="form-group">
            <button type="button" class="btn btn-primary" (click)="loadReports()">
              <i class="fas fa-sync-alt"></i>
              Refresh
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p>Loading reports...</p>
  </div>

  <!-- Statistics Cards -->
  <div *ngIf="!isLoading && statistics" class="statistics-section">
    <div class="stats-grid">
      <div class="stat-card primary">
        <div class="stat-icon">
          <i class="fas fa-file-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.totalRequests }}</div>
          <div class="stat-label">Total Requests</div>
        </div>
      </div>

      <div class="stat-card warning">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.pendingRequests }}</div>
          <div class="stat-label">Pending Requests</div>
        </div>
      </div>

      <div class="stat-card success">
        <div class="stat-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.approvedRequests }}</div>
          <div class="stat-label">Approved Requests</div>
        </div>
      </div>

      <div class="stat-card danger">
        <div class="stat-icon">
          <i class="fas fa-times-circle"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.rejectedRequests }}</div>
          <div class="stat-label">Rejected Requests</div>
        </div>
      </div>

      <div class="stat-card info">
        <div class="stat-icon">
          <i class="fas fa-calendar-days"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.totalDaysRequested }}</div>
          <div class="stat-label">Total Days Requested</div>
        </div>
      </div>

      <div class="stat-card success">
        <div class="stat-icon">
          <i class="fas fa-calendar-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ statistics.totalDaysApproved }}</div>
          <div class="stat-label">Total Days Approved</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div *ngIf="!isLoading && statistics" class="charts-section">
    <div class="charts-grid">
      <!-- Leave Types Chart -->
      <div class="chart-card">
        <div class="chart-header">
          <h3><i class="fas fa-chart-pie"></i> Requests by Leave Type</h3>
        </div>
        <div class="chart-content">
          <div class="simple-chart">
            <div *ngFor="let item of leaveTypeChartData" class="chart-item">
              <div class="chart-bar">
                <div class="bar-fill" [style.width.%]="(item.value / statistics!.totalRequests) * 100"></div>
              </div>
              <div class="chart-label">
                <span class="label-text">{{ item.name }}</span>
                <span class="label-value">{{ item.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Leave Status Chart -->
      <div class="chart-card">
        <div class="chart-header">
          <h3><i class="fas fa-chart-donut"></i> Requests by Status</h3>
        </div>
        <div class="chart-content">
          <div class="simple-chart">
            <div *ngFor="let item of leaveStatusChartData" class="chart-item">
              <div class="chart-bar">
                <div class="bar-fill" [style.width.%]="(item.value / statistics!.totalRequests) * 100"></div>
              </div>
              <div class="chart-label">
                <span class="label-text">{{ item.name }}</span>
                <span class="label-value">{{ item.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Requests -->
  <div *ngIf="!isLoading" class="recent-requests-section">
    <div class="section-card">
      <div class="section-header">
        <h3><i class="fas fa-history"></i> Recent Leave Requests</h3>
        <a routerLink="/hr/leave-management" class="btn btn-outline-primary btn-sm">
          View All
        </a>
      </div>
      <div class="section-content">
        <div *ngIf="recentLeaves.length === 0" class="no-data">
          <i class="fas fa-inbox"></i>
          <p>No recent leave requests found</p>
        </div>
        <div *ngIf="recentLeaves.length > 0" class="requests-table">
          <div class="table-header">
            <div class="col-employee">Employee</div>
            <div class="col-type">Type</div>
            <div class="col-dates">Dates</div>
            <div class="col-days">Days</div>
            <div class="col-status">Status</div>
            <div class="col-submitted">Submitted</div>
          </div>
          <div *ngFor="let request of recentLeaves" class="table-row">
            <div class="col-employee">
              <div class="employee-info">
                <div class="employee-name">{{ request.employeeName }}</div>
                <div class="employee-email">{{ request.employeeEmail }}</div>
              </div>
            </div>
            <div class="col-type">
              <span class="leave-type-badge" [class]="getLeaveTypeClass(request.leaveType)">
                {{ leaveService.getLeaveTypeLabel(request.leaveType) }}
              </span>
            </div>
            <div class="col-dates">
              <div class="date-range">
                {{ request.startDate | date:'MMM dd' }} - {{ request.endDate | date:'MMM dd, yyyy' }}
              </div>
            </div>
            <div class="col-days">
              <span class="days-count">{{ request.days }} days</span>
            </div>
            <div class="col-status">
              <span class="status-badge" [class]="getStatusClass(request.status)">
                {{ leaveService.getLeaveStatusLabel(request.status) }}
              </span>
            </div>
            <div class="col-submitted">
              {{ request.submittedDate | date:'MMM dd, yyyy' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Back to Dashboard -->
  <div class="back-section">
    <a routerLink="/dashboard/hr" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left"></i>
      Back to HR Dashboard
    </a>
  </div>
</div>
